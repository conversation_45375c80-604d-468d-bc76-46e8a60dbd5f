import { Home, <PERSON>Alert } from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import Logo from "@/components/logo";
import { Link } from "@/i18n/navigation";

const NotFound = () => {
  return (
    <div className="relative flex min-h-screen w-full flex-col items-center justify-center overflow-hidden">
      {/* Background Elements */}
      <div className="from-primary/8 to-primary/12 absolute inset-0 animate-pulse bg-gradient-to-br via-transparent" />
      <div className="bg-primary/15 animation-delay-500 absolute top-0 left-1/2 h-96 w-96 -translate-x-1/2 animate-pulse rounded-full blur-3xl" />
      <div className="bg-secondary/10 animation-delay-1000 absolute right-1/4 bottom-1/4 h-64 w-64 animate-bounce rounded-full blur-2xl" />
      <div className="bg-accent/8 animation-delay-2000 absolute bottom-1/3 left-1/4 h-48 w-48 animate-pulse rounded-full blur-xl" />

      <div className="relative px-4 py-36 md:py-32">
        <div className="relative z-10 text-center">
          {/* Logo */}
          <div className="mb-8 flex justify-center">
            <Logo width="64px" height="64px" />
          </div>

          {/* Badge */}
          <div className="border-border bg-background/60 text-muted-foreground animate-fade-in hover:bg-background/80 mb-6 inline-flex items-center rounded-full border px-6 py-3 text-sm font-medium shadow-lg backdrop-blur-md transition-all duration-300">
            <TriangleAlert className="text-primary mr-2 h-4 w-4 animate-pulse" />
            Error 404
            <div className="bg-destructive ml-2 h-2 w-2 animate-pulse rounded-full" />
          </div>

          {/* Main Heading */}
          <h1 className="text-foreground mb-4 text-4xl font-bold tracking-tight md:text-6xl lg:text-7xl">
            Page
            <span className="from-primary via-primary/80 to-primary ml-2 bg-gradient-to-r bg-clip-text text-transparent">
              Not Found
            </span>
          </h1>

          <p className="text-muted-foreground animate-fade-in-up animation-delay-1000 mx-auto mt-8 max-w-2xl text-xl leading-relaxed">
            The page you&apos;re looking for doesn&apos;t exist or has been
            moved. Let&apos;s get you back on track to launch your token.
          </p>

          {/* CTA Buttons */}
          <div className="animate-fade-in-up animation-delay-1200 mt-12 flex flex-col items-center justify-center gap-6 sm:flex-row">
            <Button asChild size={"lg"}>
              <Link href={"/"} className="flex items-center">
                <Home className="h-6 w-6" />
                Back to Home
              </Link>
            </Button>
          </div>

          {/* Additional Help */}
          <div className="animate-fade-in-up animation-delay-1500 mt-16">
            <p className="text-muted-foreground mb-4 text-sm">
              Need help finding what you&apos;re looking for?
            </p>
            <div className="flex flex-wrap justify-center gap-4 text-sm">
              <Link
                href="/"
                className="text-primary hover:text-primary/80 transition-colors"
              >
                Home
              </Link>
              <span className="text-muted-foreground">•</span>
              <Link
                href="/create-token"
                className="text-primary hover:text-primary/80 transition-colors"
              >
                Create Token
              </Link>
              <span className="text-muted-foreground">•</span>
              <Link
                href="/tools"
                className="text-primary hover:text-primary/80 transition-colors"
              >
                Token Tools
              </Link>
              <span className="text-muted-foreground">•</span>
              <Link
                href="https://docs.dexnity.com/"
                className="text-primary hover:text-primary/80 transition-colors"
                target="_blank"
                rel="noopener noreferrer"
              >
                Documentation
              </Link>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default NotFound;
