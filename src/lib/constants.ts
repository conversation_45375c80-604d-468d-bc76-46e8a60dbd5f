// Shared constants untuk menghindari duplikasi data

export interface Language {
  code: string;
  name: string;
  flag: string;
  country: string;
}

export interface Theme {
  id: string;
  name: string;
  icon: React.ReactNode;
}

export const LANGUAGES: Language[] = [
  { code: "en", name: "English", flag: "🇺🇸", country: "United States" },
  { code: "es", name: "Español", flag: "🇪🇸", country: "España" },
  { code: "fr", name: "Français", flag: "🇫🇷", country: "France" },
  { code: "pt", name: "Português", flag: "🇧🇷", country: "Brasil" },
  { code: "hi", name: "हिन्दी", flag: "🇮🇳", country: "भारत" },
  { code: "ru", name: "Русский", flag: "🇷🇺", country: "Россия" },
  { code: "de", name: "Deutsch", flag: "🇩🇪", country: "Deutschland" },
  { code: "id", name: "Bahasa Indonesia", flag: "🇮🇩", country: "Indonesia" },
  { code: "zh", name: "中文", flag: "🇨🇳", country: "中国" },
  { code: "ja", name: "日本語", flag: "🇯🇵", country: "日本" },
  { code: "ko", name: "한국어", flag: "🇰🇷", country: "한국" },
];

// Mapping untuk browser language detection
export const LANGUAGE_MAPPING: Record<string, string> = {
  en: "en",
  "en-US": "en",
  "en-GB": "en",
  es: "es",
  "es-ES": "es",
  "es-MX": "es",
  fr: "fr",
  "fr-FR": "fr",
  pt: "pt",
  "pt-BR": "pt",
  "pt-PT": "pt",
  hi: "hi",
  "hi-IN": "hi",
  ru: "ru",
  "ru-RU": "ru",
  de: "de",
  "de-DE": "de",
  id: "id",
  "id-ID": "id",
  zh: "zh",
  "zh-CN": "zh",
  "zh-TW": "zh",
  ja: "ja",
  "ja-JP": "ja",
  ko: "ko",
  "ko-KR": "ko",
};

export const SUPPORTED_LOCALES = [
  "en",
  "es",
  "fr",
  "pt",
  "hi",
  "ru",
  "de",
  "id",
  "zh",
  "ja",
  "ko",
];

// Common icon size untuk consistency
export const ICON_SIZES = {
  sm: "w-4 h-4",
  md: "w-5 h-5",
  lg: "w-6 h-6",
} as const;

// Storage keys untuk consistency
export const STORAGE_KEYS = {
  LANGUAGE_PREFERENCE: "user-language-preference",
  NETWORK_PREFERENCE: "user-network-preference",
} as const;