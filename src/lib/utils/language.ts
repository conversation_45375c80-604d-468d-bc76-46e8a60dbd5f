// Utility functions untuk language management
import { LANGUAGE_MAPPING, SUPPORTED_LOCALES, STORAGE_KEYS } from "../constants";

/**
 * Deteksi bahasa user berdasarkan browser settings
 */
export const detectUserLanguage = (): string => {
  if (typeof window === "undefined") return "en";

  // Cek user preference yang tersimpan
  const savedLanguage = localStorage.getItem(STORAGE_KEYS.LANGUAGE_PREFERENCE);
  if (savedLanguage && SUPPORTED_LOCALES.includes(savedLanguage)) {
    return savedLanguage;
  }

  // Deteksi dari browser
  const browserLanguages = navigator.languages || [navigator.language];
  
  for (const lang of browserLanguages) {
    const mappedLang = LANGUAGE_MAPPING[lang] || LANGUAGE_MAPPING[lang.split("-")[0]];
    if (mappedLang && SUPPORTED_LOCALES.includes(mappedLang)) {
      return mappedLang;
    }
  }

  return "en"; // fallback
};

/**
 * Simpan language preference ke localStorage
 */
export const saveLanguagePreference = (locale: string): void => {
  if (typeof window !== "undefined" && SUPPORTED_LOCALES.includes(locale)) {
    localStorage.setItem(STORAGE_KEYS.LANGUAGE_PREFERENCE, locale);
  }
};

/**
 * Cek apakah user sudah pernah set language preference
 */
export const hasLanguagePreference = (): boolean => {
  if (typeof window === "undefined") return false;
  return !!localStorage.getItem(STORAGE_KEYS.LANGUAGE_PREFERENCE);
};