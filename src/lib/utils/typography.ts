// Utility functions untuk typography dan font sizing

/**
 * Kalkulasi font size berdasarkan panjang text untuk responsive design
 */
export const calculateResponsiveFontSize = (
  text1: string,
  text2: string = ""
): string => {
  const longestLine = Math.max(text1.length, text2.length);

  // Base calculation pada line terpanjang untuk memastikan layout 2-line
  if (longestLine > 50) {
    return "text-2xl md:text-4xl lg:text-5xl";
  } else if (longestLine > 35) {
    return "text-3xl md:text-5xl lg:text-6xl";
  } else if (longestLine > 22) {
    return "text-4xl md:text-6xl lg:text-7xl";
  } else {
    return "text-4xl md:text-6xl lg:text-8xl";
  }
};

/**
 * Kalkulasi font size untuk single line text
 */
export const calculateSingleLineFontSize = (text: string): string => {
  const length = text.length;

  if (length > 40) {
    return "text-lg md:text-xl lg:text-2xl";
  } else if (length > 25) {
    return "text-xl md:text-2xl lg:text-3xl";
  } else if (length > 15) {
    return "text-2xl md:text-3xl lg:text-4xl";
  } else {
    return "text-3xl md:text-4xl lg:text-5xl";
  }
};

/**
 * Truncate text dengan ellipsis
 */
export const truncateText = (text: string, maxLength: number): string => {
  if (text.length <= maxLength) return text;
  return text.slice(0, maxLength) + "...";
};