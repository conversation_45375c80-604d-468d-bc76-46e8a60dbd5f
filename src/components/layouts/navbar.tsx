"use client";

import { useState, useMemo } from "react";
import { usePathname } from "next/navigation";
import {
  MobileNav,
  MobileNavHeader,
  MobileNavDrawer,
  MobileNavToggle,
  MobileNavDropdown,
  NavbarLogo,
  NavBody,
  NavItems,
  Navbar as ResizableNavbar,
} from "../ui/resizable-navbar";
import Settings from "../settings";
import { Button } from "../ui/button";
import { useNetwork } from "../providers/network-provider";
import Footer from "./footer";

const Navbar = ({ children }: { children: React.ReactNode }) => {
  const pathname = usePathname();
  const { networks } = useNetwork();

  const navItems = useMemo(
    () => [
      {
        name: "Create Token",
        link: "#",
        children: networks.map((network) => ({
          name: `${network.name} Network`,
          subName: `Create Token on ${network.name} Network`,
          link: `/${network.id}/create-token`,
          logo: network.logo,
          status: network.id === "solana" ? "new" : "soon",
        })),
      },
      {
        name: "Token Tools",
        link: "#",
        children: networks.map((network) => ({
          name: network.name,
          subName: `Token Tools on ${network.name} Network`,
          link: `/${network.id}/tools`,
          logo: network.logo,
          status: network.id === "solana" ? "new" : "soon",
        })),
      },
      {
        name: "Blog",
        link: "/blog",
      },
    ],
    [networks],
  );

  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const [openDropdownIndex, setOpenDropdownIndex] = useState<number | null>(
    null,
  );

  return (
    <div className="relative w-full">
      <ResizableNavbar>
        {/* Desktop Navigation */}
        <NavBody>
          <NavbarLogo />
          <NavItems items={navItems} activeItem={pathname} />
          <div className="flex items-center gap-3">
            <Settings />
            <Button className="z-50" variant="default">
              Connect Wallet
            </Button>
          </div>
        </NavBody>

        {/* Mobile Navigation */}
        <MobileNav>
          <MobileNavHeader>
            <NavbarLogo />
            <MobileNavDrawer
              isOpen={isMobileMenuOpen}
              onOpenChange={setIsMobileMenuOpen}
              trigger={
                <MobileNavToggle
                  onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
                />
              }
            >
              {navItems.map((item, idx) => (
                <MobileNavDropdown
                  key={`mobile-dropdown-${idx}`}
                  item={item}
                  onItemClick={() => setIsMobileMenuOpen(false)}
                  activeItem={pathname}
                  isOpen={openDropdownIndex === idx}
                  onToggle={() =>
                    setOpenDropdownIndex(openDropdownIndex === idx ? null : idx)
                  }
                />
              ))}
              <div className="border-border mt-6 flex w-full flex-col gap-3 border-t pt-6">
                <div className="mb-3 flex items-center justify-center">
                  <Settings />
                </div>
                <Button className="w-full" variant="default">
                  Connect Wallet
                </Button>
              </div>
            </MobileNavDrawer>
          </MobileNavHeader>
        </MobileNav>
      </ResizableNavbar>
      {children}
      <Footer />
    </div>
  );
};

export default Navbar;
