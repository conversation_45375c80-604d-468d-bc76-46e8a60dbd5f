"use client";

import { useTranslations } from "next-intl";
import Link from "next/link";
import { Github, Twitter, MessageCircle, Mail, ExternalLink } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Separator } from "@/components/ui/separator";

const Footer = () => {
  const t = useTranslations("footer");

  const socialLinks = [
    {
      name: "Twitter",
      href: "https://twitter.com/dexnity",
      icon: Twitter,
    },
    {
      name: "Telegram",
      href: "https://t.me/dexnity",
      icon: MessageCircle,
    },
    {
      name: "GitHub",
      href: "https://github.com/dexnity",
      icon: Github,
    },
    {
      name: "Email",
      href: "mailto:<EMAIL>",
      icon: Mail,
    },
  ];

  const footerSections = [
    {
      title: t("sections.product.title"),
      links: [
        { name: t("sections.product.createToken"), href: "/create-token" },
        { name: t("sections.product.tokenTools"), href: "/tools" },
        { name: t("sections.product.liquidityPools"), href: "/liquidity" },
        { name: t("sections.product.analytics"), href: "/analytics" },
      ],
    },
    {
      title: t("sections.resources.title"),
      links: [
        { name: t("sections.resources.documentation"), href: "/docs" },
        { name: t("sections.resources.blog"), href: "/blog" },
        { name: t("sections.resources.tutorials"), href: "/tutorials" },
        { name: t("sections.resources.faq"), href: "/#faqs" },
      ],
    },
    {
      title: t("sections.company.title"),
      links: [
        { name: t("sections.company.about"), href: "/about" },
        { name: t("sections.company.careers"), href: "/careers" },
        { name: t("sections.company.contact"), href: "/contact" },
        { name: t("sections.company.press"), href: "/press" },
      ],
    },
    {
      title: t("sections.legal.title"),
      links: [
        { name: t("sections.legal.privacy"), href: "/privacy" },
        { name: t("sections.legal.terms"), href: "/terms" },
        { name: t("sections.legal.security"), href: "/security" },
        { name: t("sections.legal.cookies"), href: "/cookies" },
      ],
    },
  ];

  return (
    <footer className="relative w-full border-t border-border/50 bg-background/95 backdrop-blur-md">
      {/* Background Elements */}
      <div className="absolute inset-0 bg-gradient-to-br from-primary/5 via-transparent to-secondary/5" />
      <div className="absolute top-0 left-1/4 h-32 w-32 bg-primary/10 rounded-full blur-3xl" />
      <div className="absolute bottom-0 right-1/4 h-24 w-24 bg-accent/10 rounded-full blur-2xl" />

      <div className="relative mx-auto max-w-7xl px-4 py-16">
        {/* Main Footer Content */}
        <div className="grid grid-cols-1 gap-12 lg:grid-cols-5">
          {/* Brand Section */}
          <div className="lg:col-span-2">
            <div className="mb-6">
              <Link href="/" className="flex items-center space-x-3">
                <div className="flex h-10 w-10 items-center justify-center rounded-xl bg-gradient-to-br from-primary to-primary/80 text-white font-bold text-lg">
                  D
                </div>
                <span className="text-2xl font-bold bg-gradient-to-r from-primary to-primary/80 bg-clip-text text-transparent">
                  Dexnity
                </span>
              </Link>
            </div>
            <p className="text-muted-foreground mb-6 max-w-md leading-relaxed">
              {t("description")}
            </p>
            
            {/* Social Links */}
            <div className="flex items-center space-x-4">
              {socialLinks.map((social) => {
                const IconComponent = social.icon;
                return (
                  <Button
                    key={social.name}
                    variant="ghost"
                    size="sm"
                    asChild
                    className="h-10 w-10 rounded-full border border-border/50 bg-background/50 hover:bg-primary/10 hover:border-primary/30 transition-all duration-300"
                  >
                    <Link
                      href={social.href}
                      target="_blank"
                      rel="noopener noreferrer"
                      aria-label={social.name}
                    >
                      <IconComponent className="h-4 w-4" />
                    </Link>
                  </Button>
                );
              })}
            </div>
          </div>

          {/* Footer Sections */}
          {footerSections.map((section, index) => (
            <div key={index} className="space-y-4">
              <h3 className="text-foreground font-semibold text-sm uppercase tracking-wider">
                {section.title}
              </h3>
              <ul className="space-y-3">
                {section.links.map((link, linkIndex) => (
                  <li key={linkIndex}>
                    <Link
                      href={link.href}
                      className="text-muted-foreground hover:text-primary transition-colors duration-200 text-sm flex items-center group"
                    >
                      {link.name}
                      {link.href.startsWith("http") && (
                        <ExternalLink className="ml-1 h-3 w-3 opacity-0 group-hover:opacity-100 transition-opacity duration-200" />
                      )}
                    </Link>
                  </li>
                ))}
              </ul>
            </div>
          ))}
        </div>

        <Separator className="my-12 bg-border/50" />

        {/* Bottom Section */}
        <div className="flex flex-col items-center justify-between space-y-4 md:flex-row md:space-y-0">
          <div className="text-muted-foreground text-sm">
            {t("copyright", { year: new Date().getFullYear() })}
          </div>
          
          <div className="flex items-center space-x-6 text-sm">
            <Link
              href="/status"
              className="text-muted-foreground hover:text-primary transition-colors duration-200 flex items-center"
            >
              <div className="mr-2 h-2 w-2 rounded-full bg-green-500 animate-pulse" />
              {t("status")}
            </Link>
            <Link
              href="/changelog"
              className="text-muted-foreground hover:text-primary transition-colors duration-200"
            >
              {t("changelog")}
            </Link>
          </div>
        </div>
      </div>
    </footer>
  );
};

export default Footer;