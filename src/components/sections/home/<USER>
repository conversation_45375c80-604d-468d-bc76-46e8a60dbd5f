"use server";
import { <PERSON>, <PERSON>Sign, <PERSON><PERSON><PERSON>, <PERSON>rk<PERSON>, Zap } from "lucide-react";
import GridFeatureItem from "./grid-feature-item";
import { getTranslations } from "next-intl/server";

const Features = async () => {
  const t = await getTranslations("home.features");
  return (
    <section className="px-4 py-16">
      <div className="mx-auto max-w-6xl">
        <div className="mb-12 text-center">
          <h2 className="text-foreground text-3xl font-bold tracking-tight md:text-4xl lg:text-5xl">
            {t("title1")}
            <span className="from-primary via-primary/80 to-primary ml-2 bg-gradient-to-r bg-clip-text text-transparent">
              {t("title2")}
            </span>
          </h2>
          <p className="text-muted-foreground mx-auto mt-4 max-w-2xl text-lg">
            {t("subtitle")}
          </p>
        </div>
        <ul className="grid grid-cols-1 grid-rows-none gap-6 md:grid-cols-12 md:grid-rows-3 lg:gap-6 xl:max-h-[36rem] xl:grid-rows-2">
          <GridFeatureItem
            area="md:[grid-area:1/1/2/7] xl:[grid-area:1/1/2/5]"
            icon={<Zap className="text-primary h-5 w-5" />}
            title={t("feature1.title")}
            description={t("feature1.subtitle")}
          />

          <GridFeatureItem
            area="md:[grid-area:1/7/2/13] xl:[grid-area:2/1/3/5]"
            icon={<Settings className="text-primary h-5 w-5" />}
            title={t("feature2.title")}
            description={t("feature2.subtitle")}
          />

          <GridFeatureItem
            area="md:[grid-area:2/1/3/7] xl:[grid-area:1/5/3/8]"
            icon={<Code className="text-primary h-5 w-5" />}
            title={t("feature3.title")}
            description={t("feature3.subtitle")}
          />

          <GridFeatureItem
            area="md:[grid-area:2/7/3/13] xl:[grid-area:1/8/2/13]"
            icon={<DollarSign className="text-primary h-5 w-5" />}
            title={t("feature4.title")}
            description={t("feature4.subtitle")}
          />

          <GridFeatureItem
            area="md:[grid-area:3/1/4/13] xl:[grid-area:2/8/3/13]"
            icon={<Sparkles className="text-primary h-5 w-5" />}
            title={t("feature5.title")}
            description={t("feature5.subtitle")}
          />
        </ul>
      </div>
    </section>
  );
};

export default Features;
