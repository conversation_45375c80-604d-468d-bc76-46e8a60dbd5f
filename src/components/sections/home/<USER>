"use client";

import React, { useState } from "react";
import { useTranslations } from "next-intl";
import { ChevronDown, HelpCircle } from "lucide-react";
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from "@/components/ui/collapsible";
import { cn } from "@/lib/utils";

interface FAQItemProps {
  question: string;
  answer: string;
  isOpen: boolean;
  onToggle: () => void;
}

const FAQItem: React.FC<FAQItemProps> = ({ question, answer, isOpen, onToggle }) => {
  return (
    <Collapsible open={isOpen} onOpenChange={onToggle}>
      <CollapsibleTrigger className="group flex w-full items-center justify-between rounded-xl border border-border/50 bg-background/40 p-6 text-left transition-all duration-300 hover:border-primary/30 hover:bg-background/60 hover:shadow-lg backdrop-blur-md">
        <div className="flex items-center gap-4">
          <div className="bg-primary/10 group-hover:bg-primary/20 rounded-full p-2 transition-colors duration-300">
            <HelpCircle className="text-primary h-5 w-5 transition-transform duration-300 group-hover:scale-110" />
          </div>
          <h3 className="text-foreground group-hover:text-primary text-lg font-semibold transition-colors duration-300">
            {question}
          </h3>
        </div>
        <ChevronDown className={cn(
          "text-muted-foreground group-hover:text-primary h-5 w-5 transition-all duration-300",
          isOpen && "rotate-180"
        )} />
      </CollapsibleTrigger>
      <CollapsibleContent className="overflow-hidden transition-all duration-300">
        <div className="border-border/30 bg-background/20 mx-4 mt-2 rounded-lg border p-6 backdrop-blur-sm">
          <p className="text-muted-foreground leading-relaxed">
            {answer}
          </p>
        </div>
      </CollapsibleContent>
    </Collapsible>
  );
};

const FAQs: React.FC = () => {
  const t = useTranslations("home.faqs");
  const [openIndex, setOpenIndex] = useState<number | null>(0);

  const faqs = [
    {
      question: t("faq1.question"),
      answer: t("faq1.answer"),
    },
    {
      question: t("faq2.question"),
      answer: t("faq2.answer"),
    },
    {
      question: t("faq3.question"),
      answer: t("faq3.answer"),
    },
    {
      question: t("faq4.question"),
      answer: t("faq4.answer"),
    },
    {
      question: t("faq5.question"),
      answer: t("faq5.answer"),
    },
    {
      question: t("faq6.question"),
      answer: t("faq6.answer"),
    },
  ];

  const handleToggle = (index: number) => {
    setOpenIndex(openIndex === index ? null : index);
  };

  return (
    <section className="px-4 py-16">
      <div className="mx-auto max-w-4xl">
        {/* Section Header */}
        <div className="mb-12 text-center">
          <h2 className="text-foreground text-3xl font-bold tracking-tight md:text-4xl lg:text-5xl">
            {t("title1")}
            <span className="from-primary via-primary/80 to-primary ml-2 bg-gradient-to-r bg-clip-text text-transparent">
              {t("title2")}
            </span>
          </h2>
          <p className="text-muted-foreground mx-auto mt-4 max-w-2xl text-lg">
            {t("subtitle")}
          </p>
        </div>

        {/* FAQ Items */}
        <div className="space-y-4">
          {faqs.map((faq, index) => (
            <div
              key={index}
              className="animate-fade-in-up"
              style={{
                animationDelay: `${index * 100}ms`,
              }}
            >
              <FAQItem
                question={faq.question}
                answer={faq.answer}
                isOpen={openIndex === index}
                onToggle={() => handleToggle(index)}
              />
            </div>
          ))}
        </div>

        {/* Additional Help Section */}
        <div className="animate-fade-in-up animation-delay-800 mt-16 text-center">
          <div className="border-border bg-background/40 hover:bg-background/60 rounded-2xl border p-8 backdrop-blur-md transition-all duration-300 hover:shadow-lg">
            <h3 className="text-foreground mb-4 text-xl font-semibold">
              {t("helpSection.title")}
            </h3>
            <p className="text-muted-foreground mb-6 text-base">
              {t("helpSection.subtitle")}
            </p>
            <div className="flex flex-col items-center justify-center gap-4 sm:flex-row">
              <a
                href="https://docs.dexnity.com"
                target="_blank"
                rel="noopener noreferrer"
                className="text-primary hover:text-primary/80 font-medium transition-colors"
              >
                {t("helpSection.docsLink")}
              </a>
              <span className="text-muted-foreground hidden sm:inline">•</span>
              <a
                href="mailto:<EMAIL>"
                className="text-primary hover:text-primary/80 font-medium transition-colors"
              >
                {t("helpSection.contactLink")}
              </a>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default FAQs;