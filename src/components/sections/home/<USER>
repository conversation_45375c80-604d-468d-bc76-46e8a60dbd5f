"use server";
import { <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, Co<PERSON> } from "lucide-react";
import { getTranslations } from "next-intl/server";
import <PERSON><PERSON><PERSON><PERSON> from "./hero-title";
import { <PERSON><PERSON> } from "@/components/ui/button";
import Link from "next/link";

const HeroSection = async () => {
  const t = await getTranslations("home.hero");
  return (
    <div className="relative flex min-h-screen w-full flex-col items-center justify-center overflow-hidden">
      {/* Enhanced Background Elements */}
      <div className="from-primary/8 to-primary/12 absolute inset-0 animate-pulse bg-gradient-to-br via-transparent" />
      <div className="bg-primary/15 animation-delay-500 absolute top-0 left-1/2 h-96 w-96 -translate-x-1/2 animate-pulse rounded-full blur-3xl" />
      <div className="bg-secondary/10 animation-delay-1000 absolute right-1/4 bottom-1/4 h-64 w-64 animate-bounce rounded-full blur-2xl" />
      <div className="bg-accent/8 animation-delay-2000 absolute bottom-1/3 left-1/4 h-48 w-48 animate-pulse rounded-full blur-xl" />

      <div className="relative px-4 py-36 md:py-32">
        {/* Main Heading */}
        <div className="relative z-10 text-center">
          {/* Badge */}
          <div className="border-border bg-background/60 text-muted-foreground animate-fade-in hover:bg-background/80 mb-6 inline-flex items-center rounded-full border px-6 py-3 text-sm font-medium shadow-lg backdrop-blur-md transition-all duration-300 hover:scale-105">
            <Sparkles className="text-primary mr-2 h-4 w-4 animate-pulse" />
            {t("badgePill")}
            <div className="ml-2 h-2 w-2 animate-pulse rounded-full bg-green-500" />
          </div>

          <HeroTitle />

          <p className="text-muted-foreground animate-fade-in-up animation-delay-1000 mx-auto mt-8 max-w-2xl text-xl leading-relaxed">
            {t("subtitle")}
          </p>

          {/* Enhanced CTA Buttons */}
          <div className="animate-fade-in-up animation-delay-1200 mt-12 flex flex-col items-center justify-center gap-6 sm:flex-row">
            <Button size={"lg"} asChild>
              <Link href={"/create-token"}>
                {t("button1")}
                <Coins className="h-6 w-6" />
              </Link>
            </Button>

            <Button size={"lg"} asChild variant={"outline"}>
              <Link href={"/create-pair"}>
                <Play className="h-6 w-6" />
                {t("button2")}
              </Link>
            </Button>
          </div>

          {/* Enhanced Stats */}
          <div className="animate-fade-in-up animation-delay-1400 mt-20 grid grid-cols-1 gap-8 text-center sm:grid-cols-3">
            {[
              {
                number: t("stat1.title"),
                label: t("stat1.subtitle"),
                icon: Rocket,
              },
              {
                number: t("stat2.title"),
                label: t("stat2.subtitle"),
                icon: Star,
              },
              {
                number: t("stat3.title"),
                label: t("stat3.subtitle"),
                icon: Zap,
              },
            ].map((stat, index) => {
              const IconComponent = stat.icon;
              return (
                <div
                  key={index}
                  className={`group border-border/50 bg-background/40 hover:border-primary/30 hover:bg-background/60 animate-fade-in-up rounded-2xl border-2 p-6 shadow-xl backdrop-blur-md transition-all duration-500 hover:-translate-y-2 hover:scale-110 hover:shadow-2xl animation-delay-${
                    1400 + index * 200
                  }`}
                >
                  <div className="mb-3 flex justify-center">
                    <div className="bg-primary/10 group-hover:bg-primary/20 rounded-full p-3 transition-colors duration-300">
                      <IconComponent className="text-primary h-6 w-6 transition-transform duration-300 group-hover:scale-110" />
                    </div>
                  </div>
                  <div className="text-foreground group-hover:text-primary text-3xl font-bold transition-colors duration-300 md:text-4xl">
                    {stat.number}
                  </div>
                  <div className="text-muted-foreground group-hover:text-foreground text-sm transition-colors duration-300">
                    {stat.label}
                  </div>
                </div>
              );
            })}
          </div>
        </div>
      </div>
    </div>
  );
};

export default HeroSection;
