"use server";
import { <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, Co<PERSON> } from "lucide-react";
import { getTranslations } from "next-intl/server";
import <PERSON><PERSON><PERSON><PERSON> from "./hero-title";
import { <PERSON><PERSON> } from "@/components/ui/button";
import Link from "next/link";

const HeroSection = async () => {
  const t = await getTranslations("home.hero");
  return (
    <section className="relative flex min-h-screen w-full flex-col items-center justify-center overflow-hidden">
      {/* Enhanced Background Elements with Modern Gradients */}
      <div className="absolute inset-0 bg-gradient-to-br from-primary/6 via-transparent to-secondary/8 animate-pulse" />
      <div className="absolute inset-0 bg-[radial-gradient(ellipse_at_center,_var(--primary)_0%,_transparent_50%)] opacity-10" />

      {/* Floating Orbs with Better Animation */}
      <div className="absolute top-1/4 left-1/2 h-96 w-96 -translate-x-1/2 animate-pulse rounded-full bg-gradient-to-r from-primary/20 to-secondary/15 blur-3xl animation-delay-500" />
      <div className="absolute right-1/4 bottom-1/3 h-64 w-64 animate-bounce rounded-full bg-gradient-to-l from-accent/15 to-primary/10 blur-2xl animation-delay-1000" />
      <div className="absolute bottom-1/4 left-1/4 h-48 w-48 animate-pulse rounded-full bg-gradient-to-tr from-secondary/12 to-accent/8 blur-xl animation-delay-2000" />

      {/* Grid Pattern Overlay */}
      <div className="absolute inset-0 bg-[linear-gradient(rgba(255,255,255,.02)_1px,transparent_1px),linear-gradient(90deg,rgba(255,255,255,.02)_1px,transparent_1px)] bg-[size:50px_50px] [mask-image:radial-gradient(ellipse_at_center,black_40%,transparent_70%)]" />

      <div className="relative px-6 py-40 md:py-36 lg:px-8">
        {/* Main Content Container */}
        <div className="relative z-10 mx-auto max-w-6xl text-center">
          {/* Enhanced Badge */}
          <div className="group mb-8 inline-flex items-center rounded-full border border-border/50 bg-background/70 px-8 py-4 text-sm font-semibold text-muted-foreground shadow-2xl backdrop-blur-xl transition-all duration-500 hover:scale-105 hover:bg-background/90 hover:shadow-3xl animate-fade-in">
            <Sparkles className="mr-3 h-5 w-5 animate-pulse text-primary transition-transform duration-300 group-hover:rotate-12" />
            {t("badgePill")}
            <div className="ml-3 h-2.5 w-2.5 animate-pulse rounded-full bg-gradient-to-r from-green-400 to-emerald-500 shadow-lg" />
          </div>

          <HeroTitle />

          <p className="mx-auto mt-10 max-w-3xl text-xl leading-relaxed text-muted-foreground animate-fade-in-up animation-delay-1000 lg:text-2xl lg:leading-relaxed">
            {t("subtitle")}
          </p>

          {/* Enhanced CTA Buttons */}
          <div className="mt-14 flex flex-col items-center justify-center gap-6 animate-fade-in-up animation-delay-1200 sm:flex-row lg:gap-8">
            <Button
              size={"lg"}
              asChild
              className="group relative overflow-hidden bg-gradient-to-r from-primary to-primary/90 px-8 py-4 text-lg font-semibold shadow-2xl transition-all duration-300 hover:scale-105 hover:shadow-3xl hover:from-primary/90 hover:to-primary"
            >
              <Link href={"/create-token"} className="flex items-center gap-3">
                {t("button1")}
                <Coins className="h-6 w-6 transition-transform duration-300 group-hover:rotate-12" />
              </Link>
            </Button>

            <Button
              size={"lg"}
              asChild
              variant={"outline"}
              className="group relative overflow-hidden border-2 border-border/50 bg-background/50 px-8 py-4 text-lg font-semibold backdrop-blur-xl transition-all duration-300 hover:scale-105 hover:border-primary/50 hover:bg-background/80 hover:shadow-2xl"
            >
              <Link href={"/create-pair"} className="flex items-center gap-3">
                <Play className="h-6 w-6 transition-transform duration-300 group-hover:scale-110" />
                {t("button2")}
              </Link>
            </Button>
          </div>

          {/* Enhanced Stats */}
          <div className="mt-24 grid grid-cols-1 gap-8 text-center animate-fade-in-up animation-delay-1400 sm:grid-cols-3 lg:gap-12">
            {[
              {
                number: t("stat1.title"),
                label: t("stat1.subtitle"),
                icon: Rocket,
                gradient: "from-blue-500/20 to-purple-500/20",
                iconBg: "bg-gradient-to-r from-blue-500/10 to-purple-500/10",
              },
              {
                number: t("stat2.title"),
                label: t("stat2.subtitle"),
                icon: Star,
                gradient: "from-emerald-500/20 to-teal-500/20",
                iconBg: "bg-gradient-to-r from-emerald-500/10 to-teal-500/10",
              },
              {
                number: t("stat3.title"),
                label: t("stat3.subtitle"),
                icon: Zap,
                gradient: "from-orange-500/20 to-red-500/20",
                iconBg: "bg-gradient-to-r from-orange-500/10 to-red-500/10",
              },
            ].map((stat, index) => {
              const IconComponent = stat.icon;
              return (
                <div
                  key={index}
                  className={`group relative overflow-hidden rounded-3xl border border-border/30 bg-gradient-to-br ${stat.gradient} p-8 shadow-2xl backdrop-blur-xl transition-all duration-500 hover:-translate-y-3 hover:scale-105 hover:shadow-3xl animate-fade-in-up`}
                  style={{ animationDelay: `${1400 + index * 200}ms` }}
                >
                  <div className="absolute inset-0 bg-gradient-to-br from-white/5 to-transparent opacity-0 transition-opacity duration-300 group-hover:opacity-100" />

                  <div className="relative z-10">
                    <div className="mb-6 flex justify-center">
                      <div className={`${stat.iconBg} group-hover:scale-110 rounded-2xl p-4 transition-all duration-300 group-hover:shadow-lg`}>
                        <IconComponent className="h-8 w-8 text-primary transition-transform duration-300 group-hover:rotate-12" />
                      </div>
                    </div>
                    <div className="mb-3 text-4xl font-bold text-foreground transition-all duration-300 group-hover:scale-110 md:text-5xl">
                      {stat.number}
                    </div>
                    <div className="text-base font-medium text-muted-foreground transition-colors duration-300 group-hover:text-foreground">
                      {stat.label}
                    </div>
                  </div>
                </div>
              );
            })}
          </div>
        </div>
      </div>
    </div>
  );
};

export default HeroSection;
