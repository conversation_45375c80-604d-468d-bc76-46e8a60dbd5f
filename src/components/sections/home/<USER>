"use client";
import React, { useEffect, useState } from "react";
import { useTranslations } from "next-intl";
import { calculateResponsiveFontSize } from "@/lib/utils/typography";

interface HeroTitleProps {
  className?: string;
}

const HeroTitle: React.FC<HeroTitleProps> = ({ className = "" }) => {
  const t = useTranslations("home.hero");
  const [fontSize, setFontSize] = useState("text-4xl md:text-6xl lg:text-8xl");

  const title1 = t("title1");
  const title2 = t("title2");

  // Menggunakan utility function untuk kalkulasi font size

  useEffect(() => {
    if (title1 && title2) {
      const newFontSize = calculateResponsiveFontSize(title1, title2);
      setFontSize(newFontSize);
    }
  }, [title1, title2]);

  return (
    <h1
      className={`text-foreground mx-auto max-w-5xl font-bold tracking-tight ${fontSize} ${className}`}
    >
      <div className="flex flex-col gap-2">
        <span className="leading-tight">{title1}</span>
        <span className="from-primary via-primary/80 to-primary animate-fade-in animation-delay-800 bg-gradient-to-r bg-clip-text leading-tight text-transparent">
          {title2}
        </span>
      </div>
    </h1>
  );
};

export default HeroTitle;
