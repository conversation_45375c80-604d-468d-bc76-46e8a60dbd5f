"use client";

import { useState, useEffect } from "react";
import { motion } from "framer-motion";
import {
  <PERSON><PERSON><PERSON>2,
  <PERSON>,
  Settings as SettingsIcon,
  Sun,
  Check,
  Globe,
  Palette,
  Network,
  ChevronDown,
} from "lucide-react";
import { Button } from "./ui/button";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON>eader,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  Sheet<PERSON>rigger,
} from "./ui/sheet";
import {
  Drawer,
  DrawerContent,
  DrawerHeader,
  DrawerTitle,
  DrawerTrigger,
} from "./ui/drawer";

import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from "./ui/collapsible";
import { useNetwork } from "./providers/network-provider";
import { useLanguage } from "./providers/lang-provider";
import { useTheme } from "next-themes";
import { useLocale } from "next-intl";
import Image from "next/image";
import { Badge } from "./ui/badge";
import { LANGUAGES, ICON_SIZES } from "@/lib/constants";
import { cn } from "@/lib/utils";

const Settings = () => {
  const [isOpen, setIsOpen] = useState(false);
  const [isMobile, setIsMobile] = useState(false);
  const { currentNetwork, networks, changeNetwork } = useNetwork();
  const { changeLanguage } = useLanguage();
  const { theme, setTheme } = useTheme();
  const locale = useLocale();

  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth < 768);
    };

    checkMobile();
    window.addEventListener("resize", checkMobile);

    return () => window.removeEventListener("resize", checkMobile);
  }, []);

  // Menggunakan data languages dari constants

  const themes = [
    { id: "light", name: "Light", icon: Sun, description: "Light theme" },
    { id: "dark", name: "Dark", icon: Moon, description: "Dark theme" },
    {
      id: "system",
      name: "System",
      icon: Laptop2,
      description: "Follow system preference",
    },
  ];

  const SettingsContent = () => (
    <div className="space-y-6">
      {/* Network Section */}
      <div className="space-y-3">
        <div className="flex items-center gap-2 px-1">
          <Network className="h-4 w-4" />
          <h3 className="font-medium">Network</h3>
        </div>
        <div className="grid gap-2">
          {networks.map((network) => {
            const isSelected = currentNetwork === network.id;
            const isDisabled = network.status !== "active";
            return (
              <Button
                key={network.id}
                variant="ghost"
                className={cn(
                  "hover:bg-muted/50 h-auto w-full justify-start p-3 transition-all",
                  isSelected && "bg-primary/10 border-primary/20 border",
                  isDisabled && "cursor-not-allowed opacity-50",
                )}
                onClick={() => {
                  if (network.status === "active") {
                    changeNetwork(network.id);
                  }
                }}
                disabled={isDisabled}
              >
                <Image
                  src={network.logo}
                  alt={network.name}
                  width={24}
                  height={24}
                  className="mr-3 rounded-sm"
                />
                <div className="flex flex-1 flex-col items-start">
                  <div className="flex items-center gap-2">
                    <span className="font-medium">{network.name}</span>
                    {network.status === "soon" && (
                      <Badge variant="secondary" className="text-xs">
                        Soon
                      </Badge>
                    )}
                    {network.status === "active" && network.id !== "solana" && (
                      <Badge variant="default" className="text-xs">
                        New
                      </Badge>
                    )}
                  </div>
                  <span className="text-muted-foreground text-xs">
                    {network.description}
                  </span>
                </div>
                {isSelected && (
                  <Check className={cn(ICON_SIZES.sm, "text-primary")} />
                )}
              </Button>
            );
          })}
        </div>
      </div>

      {/* Theme Section - Collapsible */}
      <Collapsible>
        <CollapsibleTrigger className="hover:bg-accent flex w-full items-center justify-between rounded-lg border p-3 text-left transition-colors">
          <div className="flex items-center gap-2">
            <Palette className="h-4 w-4" />
            <span className="font-medium">Theme</span>
          </div>
          <ChevronDown className="h-4 w-4 transition-transform duration-200 data-[state=open]:rotate-180" />
        </CollapsibleTrigger>
        <CollapsibleContent className="mt-2 space-y-2">
          {themes.map((themeOption) => {
            const IconComponent = themeOption.icon;
            const isSelected = theme === themeOption.id;
            return (
              <Button
                key={themeOption.id}
                variant="ghost"
                className={cn(
                  "hover:bg-muted/50 h-auto w-full justify-start p-3 transition-all",
                  isSelected && "bg-primary/10 border-primary/20 border",
                )}
                onClick={() => setTheme(themeOption.id)}
              >
                <IconComponent className={cn(ICON_SIZES.sm, "mr-3")} />
                <div className="flex flex-1 flex-col items-start">
                  <span className="font-medium">{themeOption.name}</span>
                  <span className="text-muted-foreground text-xs">
                    {themeOption.description}
                  </span>
                </div>
                {isSelected && (
                  <Check className={cn(ICON_SIZES.sm, "text-primary")} />
                )}
              </Button>
            );
          })}
        </CollapsibleContent>
      </Collapsible>

      {/* Language Section - Collapsible */}
      <Collapsible>
        <CollapsibleTrigger className="hover:bg-accent flex w-full items-center justify-between rounded-lg border p-3 text-left transition-colors">
          <div className="flex items-center gap-2">
            <Globe className="h-4 w-4" />
            <span className="font-medium">Language</span>
          </div>
          <ChevronDown className="h-4 w-4 transition-transform duration-200 data-[state=open]:rotate-180" />
        </CollapsibleTrigger>
        <CollapsibleContent className="mt-2 space-y-2">
          {LANGUAGES.map((language, index) => {
            const isSelected = locale === language.code;
            return (
              <motion.div
                key={language.code}
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.3, delay: 0.1 + index * 0.05 }}
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
              >
                <Button
                  variant="ghost"
                  className={cn(
                    "relative h-auto w-full justify-start p-3 transition-all duration-200 hover:bg-accent hover:text-accent-foreground rounded-xl border border-transparent",
                    isSelected && "bg-primary/10 border-primary/20 text-foreground shadow-lg",
                  )}
                  onClick={() => {
                    changeLanguage(language.code);
                    setIsOpen(false);
                  }}
                >
                  {isSelected && (
                    <motion.div
                      layoutId="language-selected"
                      className="absolute inset-0 rounded-xl bg-primary/5"
                      transition={{
                        type: "spring",
                        stiffness: 400,
                        damping: 30,
                      }}
                    />
                  )}
                  <span className="mr-3 text-lg relative z-10">{language.flag}</span>
                  <div className="flex flex-1 flex-col items-start relative z-10">
                    <span className="font-medium">{language.name}</span>
                    <span className="text-muted-foreground text-xs">
                      {language.country}
                    </span>
                  </div>
                  {isSelected && (
                    <motion.div
                      initial={{ scale: 0 }}
                      animate={{ scale: 1 }}
                      transition={{ type: "spring", stiffness: 400, damping: 30 }}
                      className="relative z-10"
                    >
                      <Check className={cn(ICON_SIZES.sm, "text-primary")} />
                    </motion.div>
                  )}
                </Button>
              </motion.div>
            );
          })}
        </CollapsibleContent>
      </Collapsible>
    </div>
  );

  if (isMobile) {
    return (
      <Drawer open={isOpen} onOpenChange={setIsOpen}>
        <DrawerTrigger asChild>
          <motion.div
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
          >
            <Button 
              size="icon" 
              variant="ghost" 
              className="relative z-50 rounded-xl transition-all duration-200 hover:bg-accent hover:text-accent-foreground hover:shadow-lg"
            >
              <div className="absolute inset-0 rounded-xl bg-gradient-to-r from-blue-500/5 via-transparent to-purple-500/5" />
              <SettingsIcon className={cn(ICON_SIZES.md, "relative z-10")} />
            </Button>
          </motion.div>
        </DrawerTrigger>
        <DrawerContent className="max-h-[80vh] backdrop-blur-xl bg-background/95 border-border">
          <DrawerHeader>
            <DrawerTitle className="text-foreground">Settings</DrawerTitle>
          </DrawerHeader>
          <div className="overflow-y-auto px-4 pb-6">
            <SettingsContent />
          </div>
        </DrawerContent>
      </Drawer>
    );
  }

  return (
    <Sheet open={isOpen} onOpenChange={setIsOpen}>
      <SheetTrigger asChild>
        <motion.div
          whileHover={{ scale: 1.05 }}
          whileTap={{ scale: 0.95 }}
        >
          <Button 
            size="icon" 
            variant="ghost" 
            className="relative z-50 rounded-xl transition-all duration-200 hover:bg-accent hover:text-accent-foreground hover:shadow-lg"
          >
            <div className="absolute inset-0 rounded-xl bg-gradient-to-r from-blue-500/5 via-transparent to-purple-500/5" />
            <SettingsIcon className={cn(ICON_SIZES.md, "relative z-10")} />
          </Button>
        </motion.div>
      </SheetTrigger>
      <SheetContent className="w-96 px-4 backdrop-blur-xl bg-background/95 border-border">
        <SheetHeader>
          <SheetTitle className="flex items-center gap-2 text-foreground">
            <SettingsIcon className={ICON_SIZES.sm} />
            Settings
          </SheetTitle>
        </SheetHeader>
        <div className="mt-6 overflow-y-auto pr-2">
          <SettingsContent />
        </div>
      </SheetContent>
    </Sheet>
  );
};

export default Settings;
