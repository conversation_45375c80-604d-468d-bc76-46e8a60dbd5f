// Reusable dropdown switcher component untuk men<PERSON><PERSON><PERSON> duplikasi UI patterns
import React from "react";
import { Button } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { ChevronDown } from "lucide-react";
import { ICON_SIZES } from "@/lib/constants";
import { Skeleton } from "./skeleton";

interface SwitcherOption {
  id: string;
  label: string;
  icon?: React.ReactNode;
  disabled?: boolean;
  badge?: React.ReactNode;
  description?: string;
}

interface SwitcherDropdownProps {
  currentValue: string;
  options: SwitcherOption[];
  onValueChange: (value: string) => void;
  isLoading?: boolean;
  placeholder?: string;
  triggerIcon?: React.ReactNode;
  className?: string;
  contentClassName?: string;
  showChevron?: boolean;
  disabled?: boolean;
}

export const SwitcherDropdown: React.FC<SwitcherDropdownProps> = ({
  currentValue,
  options,
  onValueChange,
  isLoading = false,
  placeholder = "Select option",
  triggerIcon,
  className = "",
  contentClassName = "w-56",
  showChevron = true,
  disabled = false,
}) => {
  const currentOption = options.find((option) => option.id === currentValue);

  if (isLoading) {
    return <Skeleton className="h-10 w-10" />;
  }

  return (
    <DropdownMenu modal={false}>
      <DropdownMenuTrigger asChild className="z-50">
        <Button
          size="sm"
          variant="ghost"
          className={`flex items-center gap-2 ${className}`}
          disabled={disabled || isLoading}
        >
          {triggerIcon}
          {currentOption ? (
            <>
              {currentOption.icon}
              <span className="hidden sm:inline">{currentOption.label}</span>
            </>
          ) : (
            <span>{placeholder}</span>
          )}
          {showChevron && <ChevronDown className={ICON_SIZES.sm} />}
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent className={contentClassName} sideOffset={20}>
        {options.map((option) => (
          <DropdownMenuItem
            key={option.id}
            className="flex cursor-pointer items-center gap-3 p-3"
            onClick={() => !option.disabled && onValueChange(option.id)}
            disabled={option.disabled}
          >
            {option.icon && <div className="flex-shrink-0">{option.icon}</div>}
            <div className="flex flex-1 flex-col">
              <div className="flex items-center gap-2">
                <span className="font-medium">{option.label}</span>
                {option.badge}
              </div>
              {option.description && (
                <span className="text-muted-foreground text-xs">
                  {option.description}
                </span>
              )}
            </div>
            {currentValue === option.id && (
              <div className="bg-primary ml-auto h-2 w-2 rounded-full" />
            )}
          </DropdownMenuItem>
        ))}
      </DropdownMenuContent>
    </DropdownMenu>
  );
};

export default SwitcherDropdown;