"use client";
import { cn } from "@/lib/utils";
import { Menu, X } from "lucide-react";
import { motion, useScroll, useMotionValueEvent } from "motion/react";
import {
  <PERSON>er,
  Drawer<PERSON>ontent,
  Drawer<PERSON>eader,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  DrawerTrigger,
  DrawerClose,
} from "@/components/ui/drawer";
import Logo from "@/components/logo";

import React, { useRef, useState } from "react";
import Link from "next/link";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { ChevronDownIcon } from "lucide-react";
import Image from "next/image";

interface NavbarProps {
  children: React.ReactNode;
  className?: string;
}

interface NavBodyProps {
  children: React.ReactNode;
  className?: string;
  visible?: boolean;
}

interface NavItemsProps {
  items: {
    name: string;
    link: string;
    children?: {
      name: string;
      link: string;
      subName?: string;
      logo?: string;
      status?: string;
    }[];
  }[];
  className?: string;
  onItemClick?: () => void;
  activeItem?: string;
}

interface MobileNavProps {
  children: React.ReactNode;
  className?: string;
  visible?: boolean;
}

interface MobileNavHeaderProps {
  children: React.ReactNode;
  className?: string;
}

export const Navbar = ({ children, className }: NavbarProps) => {
  const ref = useRef<HTMLDivElement>(null);
  const { scrollY } = useScroll({
    target: ref,
    offset: ["start start", "end start"],
  });
  const [visible, setVisible] = useState<boolean>(false);

  useMotionValueEvent(scrollY, "change", (latest) => {
    if (latest > 100) {
      setVisible(true);
    } else {
      setVisible(false);
    }
  });

  return (
    <motion.div
      ref={ref}
      className={cn("fixed inset-x-0 top-6 z-50 w-full", className)}
    >
      {React.Children.map(children, (child) =>
        React.isValidElement(child)
          ? React.cloneElement(
              child as React.ReactElement<{ visible?: boolean }>,
              { visible }
            )
          : child
      )}
    </motion.div>
  );
};

export const NavBody = ({ children, className, visible }: NavBodyProps) => {
  return (
    <motion.div
      animate={{
        backdropFilter: visible ? "blur(20px)" : "blur(10px)",
        backgroundColor: visible
          ? "hsl(var(--background) / 0.95)"
          : "hsl(var(--background) / 0.8)",
        boxShadow: visible
          ? "0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)"
          : "0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)",
        width: visible ? "80%" : "95%",
        y: visible ? 10 : 0,
        borderRadius: visible ? "16px" : "24px",
      }}
      transition={{
        type: "spring",
        stiffness: 300,
        damping: 30,
      }}
      style={{
        minWidth: "800px",
      }}
      className={cn(
        "relative z-[60] mx-auto hidden w-full max-w-7xl flex-row items-center justify-between self-start border border-border px-6 py-3 lg:flex",
        className
      )}
    >
      <div className="absolute inset-0 rounded-[inherit] bg-primary/5" />
      {children}
    </motion.div>
  );
};

export const NavItems = ({
  items,
  className,
  onItemClick,
  activeItem,
}: NavItemsProps) => {
  const [hovered, setHovered] = useState<number | null>(null);

  return (
    <motion.div
      onMouseLeave={() => setHovered(null)}
      className={cn(
        "absolute inset-0 hidden flex-1 flex-row items-center justify-center space-x-1 text-sm font-medium text-muted-foreground lg:flex",
        className
      )}
    >
      {items.map((item, idx) => {
        const isActive = activeItem === item.link;
        const hasChildren = item.children && item.children.length > 0;

        if (hasChildren) {
          return (
            <DropdownMenu key={`dropdown-${idx}`} modal={false}>
              <DropdownMenuTrigger asChild>
                <button
                  onMouseEnter={() => setHovered(idx)}
                  className={cn(
                    "relative cursor-pointer px-4 py-2 transition-colors duration-200 hover:text-foreground flex items-center gap-1",
                    isActive && "text-foreground"
                  )}
                >
                  {(hovered === idx || isActive) && (
                    <motion.div
                      layoutId={isActive ? "active" : "hovered"}
                      className="absolute inset-0 h-full w-full rounded-xl bg-accent"
                      transition={{
                        type: "spring",
                        stiffness: 400,
                        damping: 30,
                      }}
                    />
                  )}
                  <span className="relative z-20 font-medium">{item.name}</span>
                  <ChevronDownIcon className="relative z-20 h-4 w-4" />
                </button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="center" className="w-56">
                {item.children?.map((child, childIdx) => {
                  const isDisabled = child.status === "soon";
                  return (
                    <DropdownMenuItem
                      key={`child-${childIdx}`}
                      asChild
                      className="cursor-pointer"
                      disabled={isDisabled}
                    >
                      <Link
                        href={isDisabled ? "#" : child.link}
                        onClick={
                          isDisabled ? (e) => e.preventDefault() : onItemClick
                        }
                        className={`flex items-start gap-3 p-3 ${
                          isDisabled ? "opacity-50 cursor-not-allowed" : ""
                        }`}
                      >
                        {child.logo && (
                          <Image
                            src={child.logo}
                            alt={`${child.name} logo`}
                            className="h-6 w-6 rounded-sm object-contain flex-shrink-0 mt-0.5"
                            width={24}
                            height={24}
                          />
                        )}
                        <div className="flex flex-col gap-1 flex-1">
                          <div className="flex items-center justify-between">
                            <div className="font-medium">{child.name}</div>
                            {child.status === "soon" && (
                              <span className="px-2 py-1 text-[10px] bg-orange-100 text-orange-800 rounded-full font-medium">
                                Soon
                              </span>
                            )}
                            {child.status === "new" && (
                              <span className="px-2 py-1 text-[10px] bg-green-100 text-green-800 rounded-full font-medium">
                                New
                              </span>
                            )}
                          </div>
                          {child.subName && (
                            <div className="text-xs text-muted-foreground">
                              {child.subName}
                            </div>
                          )}
                        </div>
                      </Link>
                    </DropdownMenuItem>
                  );
                })}
              </DropdownMenuContent>
            </DropdownMenu>
          );
        }

        return (
          <Link
            onMouseEnter={() => setHovered(idx)}
            onClick={onItemClick}
            className={cn(
              "relative px-4 py-2 transition-colors duration-200 hover:text-foreground",
              isActive && "text-foreground"
            )}
            key={`link-${idx}`}
            href={item.link}
          >
            {(hovered === idx || isActive) && (
              <motion.div
                layoutId={isActive ? "active" : "hovered"}
                className="absolute inset-0 h-full w-full rounded-xl bg-accent"
                transition={{
                  type: "spring",
                  stiffness: 400,
                  damping: 30,
                }}
              />
            )}
            <span className="relative z-20 font-medium">{item.name}</span>
          </Link>
        );
      })}
    </motion.div>
  );
};

export const MobileNav = ({ children, className, visible }: MobileNavProps) => {
  return (
    <motion.div
      animate={{
        backdropFilter: visible ? "blur(20px)" : "blur(10px)",
        backgroundColor: visible
          ? "hsl(var(--background) / 0.95)"
          : "hsl(var(--background) / 0.8)",
        boxShadow: visible
          ? "0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)"
          : "0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)",
        width: visible ? "90%" : "95%",
        y: visible ? 10 : 0,
        borderRadius: visible ? "12px" : "20px",
      }}
      transition={{
        type: "spring",
        stiffness: 300,
        damping: 30,
      }}
      className={cn(
        "relative z-50 mx-auto flex w-full max-w-[calc(100vw-2rem)] flex-row items-center justify-between border border-border px-4 py-3 lg:hidden",
        className
      )}
    >
      <div className="absolute inset-0 rounded-[inherit] bg-gradient-to-r from-blue-500/5 via-transparent to-purple-500/5" />
      {children}
    </motion.div>
  );
};

export const MobileNavHeader = ({
  children,
  className,
}: MobileNavHeaderProps) => {
  return (
    <div
      className={cn(
        "relative z-10 flex w-full flex-row items-center justify-between",
        className
      )}
    >
      {children}
    </div>
  );
};

export const MobileNavDrawer = ({
  children,
  className,
  isOpen,
  onOpenChange,
  trigger,
}: {
  children: React.ReactNode;
  className?: string;
  isOpen: boolean;
  onOpenChange: (open: boolean) => void;
  trigger: React.ReactNode;
}) => {
  return (
    <Drawer open={isOpen} onOpenChange={onOpenChange}>
      <DrawerTrigger asChild>{trigger}</DrawerTrigger>
      <DrawerContent className={cn("max-h-[80vh]", className)}>
        <DrawerHeader className="text-left">
          <DrawerTitle>Menu</DrawerTitle>
          <DrawerClose className="absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-secondary">
            <X className="h-4 w-4" />
            <span className="sr-only">Close</span>
          </DrawerClose>
        </DrawerHeader>
        <div className="px-4 pb-6">
          <div className="space-y-1">{children}</div>
        </div>
      </DrawerContent>
    </Drawer>
  );
};

export const MobileNavToggle = ({ onClick }: { onClick?: () => void }) => {
  return (
    <motion.button
      whileTap={{ scale: 0.95 }}
      onClick={onClick}
      className="relative z-10 rounded-lg p-2 text-muted-foreground transition-colors duration-200 hover:bg-accent hover:text-accent-foreground lg:hidden"
    >
      <Menu size={20} />
    </motion.button>
  );
};

export const MobileNavItem = ({
  href,
  children,
  onClick,
  className,
  isActive,
}: {
  href: string;
  children: React.ReactNode;
  onClick?: () => void;
  className?: string;
  isActive?: boolean;
}) => {
  return (
    <Link
      href={href}
      onClick={onClick}
      className={cn(
        "flex w-full items-center rounded-lg px-3 py-2 text-sm font-medium transition-colors hover:bg-accent hover:text-accent-foreground",
        isActive ? "bg-accent text-accent-foreground" : "text-muted-foreground",
        className
      )}
    >
      {children}
    </Link>
  );
};

export const MobileNavDropdown = ({
  item,
  onItemClick,
  activeItem,
  isOpen,
  onToggle,
}: {
  item: {
    name: string;
    link: string;
    children?: {
      name: string;
      link: string;
      subName?: string;
      logo?: string;
      status?: string;
    }[];
  };
  onItemClick?: () => void;
  activeItem?: string;
  isOpen?: boolean;
  onToggle?: () => void;
}) => {
  const hasChildren = item.children && item.children.length > 0;

  if (!hasChildren) {
    return (
      <MobileNavItem
        href={item.link}
        onClick={onItemClick}
        isActive={activeItem === item.link}
      >
        {item.name}
      </MobileNavItem>
    );
  }

  return (
    <div className="w-full">
      <button
        onClick={onToggle}
        className={cn(
          "flex w-full items-center justify-between rounded-lg px-3 py-2 text-sm font-medium transition-colors hover:bg-accent hover:text-accent-foreground text-muted-foreground"
        )}
      >
        <span>{item.name}</span>
        <ChevronDownIcon
          className={cn(
            "h-4 w-4 transition-transform duration-200",
            isOpen && "rotate-180"
          )}
        />
      </button>
      {isOpen && (
        <div className="ml-4 mt-2 space-y-1">
          {item.children?.map((child, childIdx) => {
            const isDisabled = child.status === "soon";
            return (
              <div key={`mobile-child-${childIdx}`} className="w-full">
                {isDisabled ? (
                  <div className="flex items-start gap-3 rounded-lg px-3 py-2 opacity-50 cursor-not-allowed">
                    {child.logo && (
                      <Image
                        src={child.logo}
                        alt={`${child.name} logo`}
                        className="h-5 w-5 rounded-sm object-contain flex-shrink-0 mt-0.5"
                        width={20}
                        height={20}
                      />
                    )}
                    <div className="flex flex-col gap-1 flex-1">
                      <div className="flex items-center justify-between">
                        <div className="text-sm font-medium text-muted-foreground">
                          {child.name}
                        </div>
                        <span className="px-2 py-1 text-[10px] bg-orange-100 text-orange-800 rounded-full font-medium">
                          Soon
                        </span>
                      </div>
                      {child.subName && (
                        <div className="text-xs text-muted-foreground">
                          {child.subName}
                        </div>
                      )}
                    </div>
                  </div>
                ) : (
                  <Link
                    href={child.link}
                    onClick={onItemClick}
                    className="flex items-start gap-3 rounded-lg px-3 py-2 text-sm font-medium transition-colors hover:bg-accent hover:text-accent-foreground text-muted-foreground"
                  >
                    {child.logo && (
                      <Image
                        src={child.logo}
                        alt={`${child.name} logo`}
                        className="h-5 w-5 rounded-sm object-contain flex-shrink-0 mt-0.5"
                        width={20}
                        height={20}
                      />
                    )}
                    <div className="flex flex-col gap-1 flex-1">
                      <div className="flex items-center justify-between">
                        <div className="font-medium">{child.name}</div>
                        {child.status === "new" && (
                          <span className="px-2 py-1 text-[10px] bg-green-100 text-green-800 rounded-full font-medium">
                            New
                          </span>
                        )}
                      </div>
                      {child.subName && (
                        <div className="text-xs text-muted-foreground">
                          {child.subName}
                        </div>
                      )}
                    </div>
                  </Link>
                )}
              </div>
            );
          })}
        </div>
      )}
    </div>
  );
};

export const NavbarLogo = () => {
  return (
    <Link
      href="/"
      className="relative z-50 flex items-center space-x-3 text-md font-normal"
    >
      <Logo width="32px" height="32px" />
      <span className="font-bold text-foreground">Dexnity</span>
    </Link>
  );
};

export const NavbarButton = ({
  href,
  as: Tag = "button",
  children,
  className,
  variant = "primary",
  ...props
}: {
  href?: string;
  as?: React.ElementType;
  children: React.ReactNode;
  className?: string;
  variant?: "primary" | "secondary" | "dark" | "gradient";
} & (
  | React.ComponentPropsWithoutRef<"a">
  | React.ComponentPropsWithoutRef<"button">
)) => {
  const baseStyles =
    "relative inline-flex items-center justify-center px-4 py-2 text-sm font-medium transition-all duration-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed";

  const variantStyles = {
    primary:
      "bg-primary text-primary-foreground shadow-lg hover:shadow-xl hover:-translate-y-0.5 hover:bg-primary/90",
    secondary: "bg-secondary text-secondary-foreground hover:bg-secondary/80",
    dark: "bg-foreground text-background shadow-lg hover:shadow-xl hover:-translate-y-0.5 hover:bg-foreground/90",
    gradient:
      "bg-primary text-primary-foreground shadow-lg hover:shadow-xl hover:-translate-y-0.5 hover:bg-primary/90",
  };

  return (
    <Tag
      href={href || undefined}
      className={cn(baseStyles, variantStyles[variant], className)}
      {...props}
    >
      <span className="relative z-10">{children}</span>
      {variant === "gradient" && (
        <div className="absolute inset-0 rounded-xl bg-primary/20 opacity-0 transition-opacity duration-200 hover:opacity-100" />
      )}
    </Tag>
  );
};
