"use client";

import React, {
  createContext,
  useCallback,
  useContext,
  useEffect,
  useState,
  useMemo,
} from "react";
import { STORAGE_KEYS } from "@/lib/constants";

export type NetworkType = "solana" | "evm" | "ton" | "sui";

interface Network {
  id: NetworkType;
  name: string;
  logo: string;
  description: string;
  status: "active" | "soon";
}

const NETWORKS: readonly Network[] = [
  {
    id: "solana",
    name: "Solana",
    logo: "/images/networks/solana.svg",
    description: "High-performance blockchain",
    status: "active",
  },
  {
    id: "evm",
    name: "EVM",
    logo: "/images/networks/evm.png",
    description: "Ethereum Virtual Machine",
    status: "soon",
  },
  {
    id: "ton",
    name: "TON",
    logo: "/images/networks/ton.svg",
    description: "The Open Network",
    status: "soon",
  },
  {
    id: "sui",
    name: "<PERSON><PERSON>",
    logo: "/images/networks/sui.png",
    description: "Next-gen smart contract platform",
    status: "soon",
  },
] as const;

interface NetworkContextType {
  currentNetwork: NetworkType;
  networks: readonly Network[];
  changeNetwork: (network: NetworkType) => void;
  isLoading: boolean;
  getNetworkById: (id: NetworkType) => Network | undefined;
}

const NetworkContext = createContext<NetworkContextType | undefined>(undefined);

interface NetworkProviderProps {
  children: React.ReactNode;
}

export const NetworkProvider: React.FC<NetworkProviderProps> = ({
  children,
}) => {
  const [currentNetwork, setCurrentNetwork] = useState<NetworkType>("solana");
  const [isLoading, setIsLoading] = useState(false);

  const changeNetwork = useCallback((network: NetworkType) => {
    const targetNetwork = NETWORKS.find((n) => n.id === network);
    if (!targetNetwork || targetNetwork.status !== "active") {
      console.warn(`Network ${network} is not available or not active`);
      return;
    }

    setIsLoading(true);
    setCurrentNetwork(network);

    // Save user preference with error handling
    try {
      if (typeof window !== "undefined") {
        localStorage.setItem(STORAGE_KEYS.NETWORK_PREFERENCE, network);
      }
    } catch (error) {
      console.error("Failed to save network preference:", error);
    }

    // Reset loading state after a short delay
    setTimeout(() => setIsLoading(false), 300);
  }, []);

  const getNetworkById = useCallback(
    (id: NetworkType) => NETWORKS.find((network) => network.id === id),
    [],
  );

  // Load saved network preference on mount
  useEffect(() => {
    if (typeof window !== "undefined") {
      try {
        const savedNetwork = localStorage.getItem(
          STORAGE_KEYS.NETWORK_PREFERENCE,
        ) as NetworkType;
        
        if (savedNetwork) {
          const networkExists = NETWORKS.find((n) => n.id === savedNetwork);
          const isActive = networkExists?.status === "active";
          
          if (networkExists && isActive) {
            setCurrentNetwork(savedNetwork);
          } else if (networkExists && !isActive) {
            console.warn(`Saved network ${savedNetwork} is no longer active, falling back to default`);
            // Clear invalid preference
            localStorage.removeItem(STORAGE_KEYS.NETWORK_PREFERENCE);
          }
        }
      } catch (error) {
        console.error("Failed to load network preference:", error);
      }
    }
  }, []);

  const contextValue: NetworkContextType = useMemo(
    () => ({
      currentNetwork,
      networks: NETWORKS,
      changeNetwork,
      isLoading,
      getNetworkById,
    }),
    [currentNetwork, changeNetwork, isLoading, getNetworkById],
  );

  return (
    <NetworkContext.Provider value={contextValue}>
      {children}
    </NetworkContext.Provider>
  );
};

export const useNetwork = (): NetworkContextType => {
  const context = useContext(NetworkContext);
  if (context === undefined) {
    throw new Error("useNetwork must be used within a NetworkProvider");
  }
  return context;
};

export default NetworkProvider;
