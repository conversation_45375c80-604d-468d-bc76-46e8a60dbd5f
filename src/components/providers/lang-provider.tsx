"use client";

import React, {
  createContext,
  useCallback,
  useContext,
  useEffect,
  useState,
} from "react";
import { useLocale } from "next-intl";
import { useRouter, usePathname } from "@/i18n/navigation";
import { SUPPORTED_LOCALES } from "@/lib/constants";
import {
  detectUserLanguage,
  saveLanguagePreference,
  hasLanguagePreference,
} from "@/lib/utils/language";

interface LanguageContextType {
  currentLocale: string;
  changeLanguage: (locale: string) => void;
  isLoading: boolean;
  detectedLocale?: string;
}

const LanguageContext = createContext<LanguageContextType | undefined>(
  undefined,
);

// Menggunakan constants yang sudah didefinisikan
const supportedLocales = SUPPORTED_LOCALES;

interface LangProviderProps {
  children: React.ReactNode;
}

export const LangProvider: React.FC<LangProviderProps> = ({ children }) => {
  const currentLocale = useLocale();
  const router = useRouter();
  const pathname = usePathname();
  const [isLoading, setIsLoading] = useState(false);
  const [detectedLocale, setDetectedLocale] = useState<string>();
  const [hasAutoDetected, setHasAutoDetected] = useState(false);

  const changeLanguage = useCallback(
    (locale: string) => {
      if (!supportedLocales.includes(locale)) {
        return;
      }

      setIsLoading(true);

      // Save user preference
      saveLanguagePreference(locale);

      // Navigate to the same path with new locale
      router.replace(pathname, { locale });

      // Reset loading state after a short delay
      setTimeout(() => setIsLoading(false), 300);
    },
    [router, pathname],
  );

  // Auto-detect user's preferred language on first visit
  useEffect(() => {
    if (typeof window !== "undefined" && !hasAutoDetected) {
      const detected = detectUserLanguage();
      setDetectedLocale(detected);

      // Only auto-redirect if:
      // 1. We detected a different language than current
      // 2. User hasn't manually set a language preference
      // 3. Current locale is the default (en)
      const userHasPreference = hasLanguagePreference();

      if (
        !userHasPreference &&
        currentLocale === "en" &&
        detected !== "en"
      ) {
        changeLanguage(detected);
        saveLanguagePreference(detected);
      }

      setHasAutoDetected(true);
    }
  }, [currentLocale, hasAutoDetected, changeLanguage]);

  const contextValue: LanguageContextType = {
    currentLocale,
    changeLanguage,
    isLoading,
    detectedLocale,
  };

  return (
    <LanguageContext.Provider value={contextValue}>
      {children}
    </LanguageContext.Provider>
  );
};

export const useLanguage = (): LanguageContextType => {
  const context = useContext(LanguageContext);
  if (context === undefined) {
    throw new Error("useLanguage must be used within a LangProvider");
  }
  return context;
};

export default LangProvider;
