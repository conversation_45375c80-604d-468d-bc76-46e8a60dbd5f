# ✅ Coding Standards for Next.js 15 + Bun + TypeScript

These rules define how to write clean, maintainable, and scalable code across all Next.js 15 projects using **Bun** and **TypeScript**. Follow them strictly.

---

## 📚 General Rules

1. **Use `context7` to search for documentation.**  
   Never guess APIs. Always search for best practices and official docs via [context7](https://context7.com).

2. **Write meaningful and consistent commit messages.**  
   Use prefixes like `feat:`, `fix:`, `refactor:`, etc.

3. **Use absolute imports with aliases**  
   Configure `tsconfig.json` to avoid messy relative paths.

4. **Avoid magic numbers and strings.**  
   Use constants or enums.

5. **Always handle errors explicitly.**  
   Never ignore errors from async functions or network calls.

---

## 🧠 Code Design Principles

6. **DRY (Don't Repeat Yourself)**  
   Do not duplicate code. Extract repeated logic into functions, hooks, or components.

7. **KISS (Keep It Simple, Stupid)**  
   Keep solutions as simple as possible. Avoid overengineering.

8. **SoC (Separation of Concerns)**  
   Clearly separate UI, logic, and data-fetching layers.

9. **YAGNI (You Aren't Gonna Need It)**  
   Do not write code or features that aren't immediately necessary.

10. **Single Responsibility Principle (SRP)**  
    Each function, file, or component should do one thing only.

---

## 🧰 Tooling & Tech Rules

11. **Use Bun for everything.**  
    ❌ No `npm`, `npx`, or `yarn`  
    ✅ Use `bun`, `bunx`, `bun run`, `bun install`

12. **Use TypeScript everywhere.**

- Strict mode must be enabled.
- Never use `any`.
- Always define explicit types for function arguments and returns.

13. **Use `tsx` for all React files.**  
    Even if there are no JSX elements yet.

14. **Group logic in custom hooks or services.**  
    Keep components clean and focused on rendering only.

---

## 🧼 Clean Code Practices

16. **No commented-out code in commits.**  
    Remove all unused code before committing.

17. **Use ESLint and Prettier consistently.**  
    Auto-format on save. Use the same config across the team.

18. **Use environment variables responsibly.**  
    Never commit `.env.local` or secret keys to version control.

19. **Use `zod` or similar schema validators for input validation.**  
    Never trust raw data (e.g., API responses, form inputs).

20. **Prefer `const` over `let`, avoid `var`.**  
    Immutability reduces bugs.

---

## ✅ Final Checklist Before Commit

- [ ] Code is DRY and simple
- [ ] Proper file and folder organization
- [ ] No use of `any` or loosely typed variables
- [ ] Error handling implemented
- [ ] All logic extracted from UI components
- [ ] Code formatted (Prettier + ESLint)
- [ ] Bun is used for all scripts and installs
- [ ] Commit message is meaningful and clean
- [ ] Documentation searched with `context7` if unsure

---

**Tech Stack Reference:**

- [Next.js 15](https://nextjs.org/)
- [TypeScript](https://www.typescriptlang.org/)
- [Context7](https://context7.com/)
